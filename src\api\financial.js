import request from './request'

export default {
  // 获取财务记录列表
  getFinancialRecords(params) {
    return request({
      url: '/financial/records',
      method: 'get',
      params
    })
  },

  // 获取财务统计数据
  getFinancialStats(params) {
    return request({
      url: '/financial/stats',
      method: 'get',
      params
    })
  },

  // 创建财务记录
  createFinancialRecord(data) {
    return request({
      url: '/financial/records',
      method: 'post',
      data
    })
  },

  // 更新财务记录
  updateFinancialRecord(id, data) {
    return request({
      url: `/financial/records/${id}`,
      method: 'put',
      data
    })
  },

  // 删除财务记录
  deleteFinancialRecord(id) {
    return request({
      url: `/financial/records/${id}`,
      method: 'delete'
    })
  },

  // 审核财务记录
  approveFinancialRecord(id, data) {
    return request({
      url: `/financial/records/${id}/approve`,
      method: 'put',
      data
    })
  },

  // 导出财务数据
  exportFinancialData(params) {
    return request({
      url: '/financial/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}
