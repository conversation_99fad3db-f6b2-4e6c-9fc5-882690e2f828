<template>
  <div class="financial-list-container">
    <div class="page-header">
      <h2>财务管理</h2>
      <el-button type="primary" @click="showAddDialog" :icon="Plus">
        添加记录
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon income">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="amount">¥{{ formatAmount(totalIncome) }}</div>
              <div class="label">总收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon expense">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="stats-info">
              <div class="amount">¥{{ formatAmount(totalExpense) }}</div>
              <div class="label">总支出</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon profit">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="stats-info">
              <div class="amount">¥{{ formatAmount(totalProfit) }}</div>
              <div class="label">净利润</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="amount">{{ pendingCount }}</div>
              <div class="label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select
            v-model="searchForm.type"
            placeholder="选择类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="收入" value="INCOME" />
            <el-option label="支出" value="EXPENSE" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.category"
            placeholder="选择分类"
            clearable
            @change="handleSearch"
          >
            <el-option label="项目收入" value="PROJECT_INCOME" />
            <el-option label="办公用品" value="OFFICE_SUPPLIES" />
            <el-option label="差旅费用" value="TRAVEL" />
            <el-option label="培训费用" value="TRAINING" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="searchForm.description"
            placeholder="搜索描述"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetSearch" :icon="Refresh"> 重置 </el-button>
          <el-button type="success" @click="exportData" :icon="Download">
            导出
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      :data="financialList"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="type" label="类型" width="100">
        <template #default="{ row }">
          <el-tag
            :type="row.type === 'INCOME' ? 'success' : 'danger'"
            size="small"
          >
            {{ row.type === "INCOME" ? "收入" : "支出" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="分类" width="120">
        <template #default="{ row }">
          {{ getCategoryLabel(row.category) }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额" width="120">
        <template #default="{ row }">
          <span
            :class="row.type === 'INCOME' ? 'income-amount' : 'expense-amount'"
          >
            {{ row.type === "INCOME" ? "+" : "-" }}¥{{
              row.amount?.toLocaleString()
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" show-overflow-tooltip />
      <el-table-column prop="date" label="日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.date) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdBy" label="创建人" width="100" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
            :icon="Edit"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.status === 'PENDING'"
            type="success"
            size="small"
            @click="handleApprove(row)"
          >
            审核
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
            :icon="Delete"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 财务记录表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        :model="financialForm"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="financialForm.type">
            <el-radio label="INCOME">收入</el-radio>
            <el-radio label="EXPENSE">支出</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="financialForm.category"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option label="项目收入" value="PROJECT_INCOME" />
            <el-option label="办公用品" value="OFFICE_SUPPLIES" />
            <el-option label="差旅费用" value="TRAVEL" />
            <el-option label="培训费用" value="TRAINING" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input
            v-model.number="financialForm.amount"
            placeholder="请输入金额"
            type="number"
            :min="0"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="financialForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="financialForm.date"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Search,
  Refresh,
  Download,
  Edit,
  Delete,
  TrendCharts,
  ShoppingCart,
  Wallet,
  Clock,
} from "@element-plus/icons-vue";
import financialAPI from "@/api/financial";

export default {
  name: "FinancialList",
  setup() {
    const loading = ref(false);
    const submitting = ref(false);
    const dialogVisible = ref(false);
    const isEdit = ref(false);
    const formRef = ref(null);

    const financialList = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    // 统计数据
    const totalIncome = ref(0);
    const totalExpense = ref(0);
    const pendingCount = ref(0);

    const totalProfit = computed(() => totalIncome.value - totalExpense.value);

    const searchForm = reactive({
      type: "",
      category: "",
      dateRange: [],
      description: "",
    });

    const financialForm = reactive({
      id: null,
      type: "EXPENSE",
      category: "",
      amount: null,
      description: "",
      date: "",
    });

    const formRules = {
      type: [{ required: true, message: "请选择类型", trigger: "change" }],
      category: [{ required: true, message: "请选择分类", trigger: "change" }],
      amount: [
        { required: true, message: "请输入金额", trigger: "blur" },
        {
          type: "number",
          min: 0.01,
          message: "金额必须大于0",
          trigger: "blur",
        },
      ],
      description: [{ required: true, message: "请输入描述", trigger: "blur" }],
      date: [{ required: true, message: "请选择日期", trigger: "change" }],
    };

    const dialogTitle = computed(() => {
      return isEdit.value ? "编辑财务记录" : "添加财务记录";
    });

    // 模拟数据
    const mockData = [
      {
        id: 1,
        type: "INCOME",
        category: "PROJECT_INCOME",
        amount: 100000,
        description: "企业管理系统项目收入",
        date: "2024-01-15",
        status: "APPROVED",
        createdBy: "张三",
      },
      {
        id: 2,
        type: "EXPENSE",
        category: "OFFICE_SUPPLIES",
        amount: 5000,
        description: "办公用品采购",
        date: "2024-01-10",
        status: "PENDING",
        createdBy: "李四",
      },
    ];

    // 获取财务列表
    const getFinancialList = async () => {
      try {
        loading.value = true;

        // 构建查询参数
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          type: searchForm.type || undefined,
          category: searchForm.category || undefined,
          description: searchForm.description || undefined,
          startDate: searchForm.dateRange?.[0] || undefined,
          endDate: searchForm.dateRange?.[1] || undefined,
        };

        // 调用真实API
        const response = await financialAPI.getFinancialRecords(params);
        const { data } = response.data;

        financialList.value = data.records || [];
        total.value = data.pagination?.total || 0;

        // 获取统计数据
        await getFinancialStats();
      } catch (error) {
        console.error("获取财务列表失败:", error);
        // 如果API调用失败，使用模拟数据作为后备
        financialList.value = mockData;
        total.value = mockData.length;

        // 计算统计数据
        totalIncome.value = mockData
          .filter((item) => item.type === "INCOME")
          .reduce((sum, item) => sum + item.amount, 0);
        totalExpense.value = mockData
          .filter((item) => item.type === "EXPENSE")
          .reduce((sum, item) => sum + item.amount, 0);
        totalProfit.value = totalIncome.value - totalExpense.value;
        pendingCount.value = mockData.filter(
          (item) => item.status === "PENDING"
        ).length;

        ElMessage.warning("使用模拟数据，请检查后端服务连接");
      } finally {
        loading.value = false;
      }
    };

    // 获取财务统计数据
    const getFinancialStats = async () => {
      try {
        const response = await financialAPI.getFinancialStats();
        const { data } = response.data;

        totalIncome.value = data.totalIncome || 0;
        totalExpense.value = data.totalExpense || 0;
        totalProfit.value = data.totalProfit || 0;
        pendingCount.value = data.pendingCount || 0;
      } catch (error) {
        console.error("获取统计数据失败:", error);
      }
    };

    // 搜索
    const handleSearch = () => {
      currentPage.value = 1;
      getFinancialList();
    };

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        type: "",
        category: "",
        dateRange: [],
        description: "",
      });
      handleSearch();
    };

    // 分页
    const handleSizeChange = (size) => {
      pageSize.value = size;
      getFinancialList();
    };

    const handleCurrentChange = (page) => {
      currentPage.value = page;
      getFinancialList();
    };

    // 显示添加对话框
    const showAddDialog = () => {
      isEdit.value = false;
      financialForm.date = new Date().toISOString().split("T")[0];
      dialogVisible.value = true;
    };

    // 编辑记录
    const handleEdit = (row) => {
      isEdit.value = true;
      Object.assign(financialForm, { ...row });
      dialogVisible.value = true;
    };

    // 审核记录
    const handleApprove = async (row) => {
      try {
        await ElMessageBox.confirm(`确定要审核通过此记录吗？`, "确认审核", {
          type: "warning",
        });

        ElMessage.success("审核成功");
        getFinancialList();
      } catch (error) {
        if (error !== "cancel") {
          ElMessage.error("审核失败");
        }
      }
    };

    // 删除记录
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除此财务记录吗？此操作不可恢复！`,
          "确认删除",
          { type: "warning" }
        );

        ElMessage.success("删除成功");
        getFinancialList();
      } catch (error) {
        if (error !== "cancel") {
          ElMessage.error("删除失败");
        }
      }
    };

    // 导出数据
    const exportData = () => {
      ElMessage.info("导出功能开发中...");
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const valid = await formRef.value.validate();
        if (!valid) return;

        submitting.value = true;

        if (isEdit.value) {
          ElMessage.success("财务记录更新成功");
        } else {
          ElMessage.success("财务记录创建成功");
        }

        dialogVisible.value = false;
        getFinancialList();
      } catch (error) {
        ElMessage.error(error.message || "操作失败");
      } finally {
        submitting.value = false;
      }
    };

    // 重置表单
    const resetForm = () => {
      Object.assign(financialForm, {
        id: null,
        type: "EXPENSE",
        category: "",
        amount: null,
        description: "",
        date: "",
      });
      formRef.value?.resetFields();
    };

    // 工具函数
    const getCategoryLabel = (category) => {
      const categoryLabels = {
        PROJECT_INCOME: "项目收入",
        OFFICE_SUPPLIES: "办公用品",
        TRAVEL: "差旅费用",
        TRAINING: "培训费用",
        OTHER: "其他",
      };
      return categoryLabels[category] || category;
    };

    const getStatusType = (status) => {
      const statusTypes = {
        PENDING: "warning",
        APPROVED: "success",
        REJECTED: "danger",
      };
      return statusTypes[status] || "info";
    };

    const getStatusLabel = (status) => {
      const statusLabels = {
        PENDING: "待审核",
        APPROVED: "已审核",
        REJECTED: "已拒绝",
      };
      return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
      if (!dateString) return "-";
      return new Date(dateString).toLocaleDateString();
    };

    const formatAmount = (amount) => {
      return amount?.toLocaleString() || "0";
    };

    onMounted(() => {
      getFinancialList();
    });

    return {
      loading,
      submitting,
      dialogVisible,
      dialogTitle,
      isEdit,
      formRef,
      financialList,
      currentPage,
      pageSize,
      total,
      totalIncome,
      totalExpense,
      totalProfit,
      pendingCount,
      searchForm,
      financialForm,
      formRules,
      getFinancialList,
      getFinancialStats,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      showAddDialog,
      handleEdit,
      handleApprove,
      handleDelete,
      exportData,
      handleSubmit,
      resetForm,
      getCategoryLabel,
      getStatusType,
      getStatusLabel,
      formatDate,
      formatAmount,
      Plus,
      Search,
      Refresh,
      Download,
      Edit,
      Delete,
      TrendCharts,
      ShoppingCart,
      Wallet,
      Clock,
    };
  },
};
</script>

<style lang="scss" scoped>
.financial-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stats-card {
    .stats-content {
      display: flex;
      align-items: center;

      .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 24px;
        color: white;

        &.income {
          background: #67c23a;
        }
        &.expense {
          background: #f56c6c;
        }
        &.profit {
          background: #409eff;
        }
        &.pending {
          background: #e6a23c;
        }
      }

      .stats-info {
        .amount {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 5px;
        }

        .label {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.income-amount {
  color: #67c23a;
  font-weight: 600;
}

.expense-amount {
  color: #f56c6c;
  font-weight: 600;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
