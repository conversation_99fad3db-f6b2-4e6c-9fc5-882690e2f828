<template>
  <div class="sidebar">
    <div class="sidebar-header">
      <h1 class="logo">石油管理系统</h1>
    </div>

    <el-menu
      :default-active="$route.path"
      class="sidebar-menu"
      router
      background-color="transparent"
      text-color="#3C3C43"
      active-text-color="#007AFF"
      @select="handleMenuSelect"
    >
      <el-menu-item index="/dashboard">
        <el-icon><House /></el-icon>
        <span>仪表盘</span>
      </el-menu-item>

      <el-menu-item index="/users" v-if="isManager">
        <el-icon><User /></el-icon>
        <span>用户管理</span>
      </el-menu-item>

      <el-menu-item index="/projects">
        <el-icon><FolderOpened /></el-icon>
        <span>项目管理</span>
      </el-menu-item>

      <el-menu-item index="/teams">
        <el-icon><UserFilled /></el-icon>
        <span>团队管理</span>
      </el-menu-item>

      <el-menu-item index="/financial" v-if="isManager">
        <el-icon><Money /></el-icon>
        <span>财务管理</span>
      </el-menu-item>

      <el-menu-item index="/reports">
        <el-icon><DataAnalysis /></el-icon>
        <span>报表展示</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script>
import { computed } from "vue";
import { useStore } from "vuex";
import {
  House,
  User,
  FolderOpened,
  UserFilled,
  Money,
  DataAnalysis,
} from "@element-plus/icons-vue";

export default {
  name: "Sidebar",
  components: {
    House,
    User,
    FolderOpened,
    UserFilled,
    Money,
    DataAnalysis,
  },
  setup() {
    const store = useStore();

    const isManager = computed(() => {
      const userRoles = store.getters["auth/userRoles"];
      const isAuthenticated = store.getters["auth/isAuthenticated"];

      // 调试信息
      console.log("Sidebar - isAuthenticated:", isAuthenticated);
      console.log("Sidebar - userRoles:", userRoles);

      // 如果用户未登录，暂时显示所有菜单项用于测试
      if (!isAuthenticated) {
        console.log(
          "User not authenticated, showing all menu items for testing"
        );
        return true;
      }

      return store.getters["auth/isManager"];
    });

    const handleMenuSelect = (index, indexPath, item, routerResult) => {
      console.log("Menu selected:", index);
      console.log("Index path:", indexPath);
      console.log("Router result:", routerResult);

      // 如果路由跳转失败，手动处理
      if (routerResult && routerResult.catch) {
        routerResult.catch((error) => {
          console.error("Router navigation error:", error);
        });
      }
    };

    return {
      isManager,
      handleMenuSelect,
    };
  },
};
</script>

<style lang="scss" scoped>
.sidebar {
  width: 240px;
  background: $bg-primary;
  box-shadow: $shadow-light;
  border-right: 1px solid $separator-color;

  .sidebar-header {
    padding: $spacing-lg;
    border-bottom: 1px solid $separator-color;

    .logo {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: $text-primary;
      text-align: center;
    }
  }

  .sidebar-menu {
    border: none;

    .el-menu-item {
      height: 48px;
      line-height: 48px;
      margin: 4px 12px;
      border-radius: $border-radius-small;

      &:hover {
        background-color: rgba($primary-color, 0.1);
      }

      &.is-active {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
