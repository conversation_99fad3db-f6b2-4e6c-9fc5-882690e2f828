# 财务管理系统 API 接口文档

## 概述

本文档基于前端 `FinancialList.vue` 组件分析，定义了财务管理系统所需的后端API接口规范。

### 基础信息

- **API 基础路径**: `/api`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 数据模型

### FinancialRecord（财务记录）

```typescript
interface FinancialRecord {
  id: number;                    // 记录ID
  type: 'INCOME' | 'EXPENSE';   // 类型：收入/支出
  category: string;             // 分类
  amount: number;               // 金额（单位：分）
  description: string;          // 描述
  date: string;                 // 日期 (YYYY-MM-DD)
  status: 'PENDING' | 'APPROVED' | 'REJECTED'; // 状态
  createdBy: string;           // 创建人
  createdAt: string;           // 创建时间
  updatedAt: string;           // 更新时间
}
```

### 财务分类枚举

```typescript
enum FinancialCategory {
  PROJECT_INCOME = 'PROJECT_INCOME',    // 项目收入
  OFFICE_SUPPLIES = 'OFFICE_SUPPLIES',  // 办公用品
  TRAVEL = 'TRAVEL',                    // 差旅费用
  TRAINING = 'TRAINING',                // 培训费用
  OTHER = 'OTHER'                       // 其他
}
```

### 统计数据模型

```typescript
interface FinancialStats {
  totalIncome: number;      // 总收入
  totalExpense: number;     // 总支出
  totalProfit: number;      // 净利润
  pendingCount: number;     // 待审核数量
}
```

## API 接口清单

### 1. 获取财务记录列表

**接口路径**: `GET /api/financial/records`

**功能描述**: 分页查询财务记录列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| size | number | 否 | 每页数量，默认10 |
| type | string | 否 | 类型筛选：INCOME/EXPENSE |
| category | string | 否 | 分类筛选 |
| startDate | string | 否 | 开始日期 (YYYY-MM) |
| endDate | string | 否 | 结束日期 (YYYY-MM) |
| description | string | 否 | 描述关键词搜索 |
| status | string | 否 | 状态筛选 |

**响应数据**:

```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "type": "INCOME",
        "category": "PROJECT_INCOME",
        "amount": 10000000,
        "description": "企业管理系统项目收入",
        "date": "2024-01-15",
        "status": "APPROVED",
        "createdBy": "张三",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

### 2. 获取财务统计数据

**接口路径**: `GET /api/financial/stats`

**功能描述**: 获取财务统计汇总数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 统计开始日期 |
| endDate | string | 否 | 统计结束日期 |

**响应数据**:

```json
{
  "success": true,
  "data": {
    "totalIncome": 50000000,
    "totalExpense": 20000000,
    "totalProfit": 30000000,
    "pendingCount": 5
  }
}
```

### 3. 创建财务记录

**接口路径**: `POST /api/financial/records`

**功能描述**: 创建新的财务记录

**请求体**:

```json
{
  "type": "EXPENSE",
  "category": "OFFICE_SUPPLIES",
  "amount": 500000,
  "description": "办公用品采购",
  "date": "2024-01-15"
}
```

**字段验证规则**:

- `type`: 必填，枚举值 INCOME/EXPENSE
- `category`: 必填，有效的分类值
- `amount`: 必填，大于0的数字（单位：分）
- `description`: 必填，长度1-500字符
- `date`: 必填，有效的日期格式

**响应数据**:

```json
{
  "success": true,
  "message": "财务记录创建成功",
  "data": {
    "id": 123,
    "type": "EXPENSE",
    "category": "OFFICE_SUPPLIES",
    "amount": 500000,
    "description": "办公用品采购",
    "date": "2024-01-15",
    "status": "PENDING",
    "createdBy": "当前用户",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 4. 更新财务记录

**接口路径**: `PUT /api/financial/records/{id}`

**功能描述**: 更新指定的财务记录

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 财务记录ID |

**请求体**: 同创建接口

**响应数据**: 同创建接口

### 5. 删除财务记录

**接口路径**: `DELETE /api/financial/records/{id}`

**功能描述**: 删除指定的财务记录

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 财务记录ID |

**响应数据**:

```json
{
  "success": true,
  "message": "财务记录删除成功"
}
```

### 6. 审核财务记录

**接口路径**: `PUT /api/financial/records/{id}/approve`

**功能描述**: 审核通过财务记录

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 财务记录ID |

**请求体**:

```json
{
  "action": "APPROVE",
  "comment": "审核意见"
}
```

**响应数据**:

```json
{
  "success": true,
  "message": "审核成功"
}
```

### 7. 导出财务数据

**接口路径**: `GET /api/financial/export`

**功能描述**: 导出财务数据为Excel文件

**请求参数**: 同查询列表接口

**响应**: 文件流（Excel格式）

## 认证和权限

### 认证方式

所有API接口都需要在请求头中携带认证令牌：

```
Authorization: Bearer <token>
```

### 权限控制

| 操作 | 所需权限 |
|------|----------|
| 查看财务记录 | financial:read |
| 创建财务记录 | financial:create |
| 更新财务记录 | financial:update |
| 删除财务记录 | financial:delete |
| 审核财务记录 | financial:approve |
| 导出财务数据 | financial:export |

## HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 错误码定义

| 错误码 | 说明 |
|--------|------|
| INVALID_PARAMS | 请求参数无效 |
| RECORD_NOT_FOUND | 财务记录不存在 |
| INSUFFICIENT_PERMISSION | 权限不足 |
| DUPLICATE_RECORD | 重复记录 |
| INVALID_STATUS | 状态无效 |

## 请求和响应示例

### 示例1：查询财务记录列表

**请求**:
```bash
GET /api/financial/records?page=1&size=10&type=INCOME&startDate=2024-01&endDate=2024-01
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "type": "INCOME",
        "category": "PROJECT_INCOME",
        "amount": 10000000,
        "description": "企业管理系统项目收入",
        "date": "2024-01-15",
        "status": "APPROVED",
        "createdBy": "张三",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 1,
      "totalPages": 1
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 示例2：创建财务记录

**请求**:
```bash
POST /api/financial/records
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "type": "EXPENSE",
  "category": "OFFICE_SUPPLIES",
  "amount": 500000,
  "description": "办公用品采购",
  "date": "2024-01-15"
}
```

**响应**:
```json
{
  "success": true,
  "message": "财务记录创建成功",
  "data": {
    "id": 123,
    "type": "EXPENSE",
    "category": "OFFICE_SUPPLIES",
    "amount": 500000,
    "description": "办公用品采购",
    "date": "2024-01-15",
    "status": "PENDING",
    "createdBy": "李四",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 示例3：错误响应

**请求**:
```bash
POST /api/financial/records
Authorization: Bearer invalid_token
Content-Type: application/json

{
  "type": "INVALID_TYPE",
  "amount": -100
}
```

**响应**:
```json
{
  "success": false,
  "message": "请求参数验证失败",
  "code": "INVALID_PARAMS",
  "errors": [
    {
      "field": "type",
      "message": "类型必须是 INCOME 或 EXPENSE"
    },
    {
      "field": "amount",
      "message": "金额必须大于0"
    },
    {
      "field": "category",
      "message": "分类不能为空"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 数据库设计建议

### 财务记录表 (financial_records)

```sql
CREATE TABLE financial_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  type ENUM('INCOME', 'EXPENSE') NOT NULL COMMENT '类型',
  category VARCHAR(50) NOT NULL COMMENT '分类',
  amount BIGINT NOT NULL COMMENT '金额(分)',
  description TEXT NOT NULL COMMENT '描述',
  date DATE NOT NULL COMMENT '日期',
  status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING' COMMENT '状态',
  created_by VARCHAR(100) NOT NULL COMMENT '创建人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_type (type),
  INDEX idx_category (category),
  INDEX idx_date (date),
  INDEX idx_status (status),
  INDEX idx_created_by (created_by),
  INDEX idx_created_at (created_at)
) COMMENT='财务记录表';
```

### 财务分类表 (financial_categories)

```sql
CREATE TABLE financial_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '分类代码',
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  type ENUM('INCOME', 'EXPENSE', 'BOTH') NOT NULL COMMENT '适用类型',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='财务分类表';
```

## 部署和配置

### 环境变量

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=financial_system
DB_USER=root
DB_PASSWORD=password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# 服务配置
PORT=8080
NODE_ENV=production

# 文件上传配置
UPLOAD_MAX_SIZE=10MB
UPLOAD_PATH=/uploads
```

### API版本控制

建议使用版本控制策略：

- URL版本控制：`/api/v1/financial/records`
- 请求头版本控制：`API-Version: v1`

### 性能优化建议

1. **分页查询优化**：使用游标分页替代偏移分页
2. **索引优化**：为常用查询字段添加复合索引
3. **缓存策略**：对统计数据使用Redis缓存
4. **数据归档**：定期归档历史数据

### 安全建议

1. **输入验证**：严格验证所有输入参数
2. **SQL注入防护**：使用参数化查询
3. **权限控制**：实现细粒度的权限控制
4. **审计日志**：记录所有敏感操作
5. **数据加密**：敏感数据加密存储

## 测试用例

### 单元测试示例

```javascript
describe('Financial Records API', () => {
  test('should create financial record successfully', async () => {
    const recordData = {
      type: 'EXPENSE',
      category: 'OFFICE_SUPPLIES',
      amount: 500000,
      description: '办公用品采购',
      date: '2024-01-15'
    };

    const response = await request(app)
      .post('/api/financial/records')
      .set('Authorization', `Bearer ${validToken}`)
      .send(recordData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.id).toBeDefined();
  });
});
```

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2024-01-15 | 初始版本，包含基础CRUD接口 |

---

**文档维护者**: 开发团队
**最后更新**: 2024-01-15
**文档版本**: v1.0.0
