openapi: 3.0.3
info:
  title: 财务管理系统 API
  description: 基于前端FinancialList.vue组件分析的财务管理系统API接口规范
  version: 1.0.0
  contact:
    name: 开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.company.com/api
    description: 生产环境

security:
  - bearerAuth: []

paths:
  /financial/records:
    get:
      summary: 获取财务记录列表
      description: 分页查询财务记录列表，支持多条件筛选
      tags:
        - 财务记录
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: type
          in: query
          description: 类型筛选
          schema:
            $ref: "#/components/schemas/FinancialType"
        - name: category
          in: query
          description: 分类筛选
          schema:
            $ref: "#/components/schemas/FinancialCategory"
        - name: startDate
          in: query
          description: 开始日期
          schema:
            type: string
            format: date
            example: "2024-01-01"
        - name: endDate
          in: query
          description: 结束日期
          schema:
            type: string
            format: date
            example: "2024-01-31"
        - name: description
          in: query
          description: 描述关键词搜索
          schema:
            type: string
            maxLength: 100
        - name: status
          in: query
          description: 状态筛选
          schema:
            $ref: "#/components/schemas/FinancialStatus"
      responses:
        "200":
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          records:
                            type: array
                            items:
                              $ref: "#/components/schemas/FinancialRecord"
                          pagination:
                            $ref: "#/components/schemas/Pagination"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

    post:
      summary: 创建财务记录
      description: 创建新的财务记录
      tags:
        - 财务记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateFinancialRecordRequest"
      responses:
        "201":
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/FinancialRecord"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /financial/records/{id}:
    put:
      summary: 更新财务记录
      description: 更新指定的财务记录
      tags:
        - 财务记录
      parameters:
        - name: id
          in: path
          required: true
          description: 财务记录ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateFinancialRecordRequest"
      responses:
        "200":
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/FinancialRecord"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

    delete:
      summary: 删除财务记录
      description: 删除指定的财务记录
      tags:
        - 财务记录
      parameters:
        - name: id
          in: path
          required: true
          description: 财务记录ID
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /financial/records/{id}/approve:
    put:
      summary: 审核财务记录
      description: 审核通过或拒绝财务记录
      tags:
        - 财务记录
      parameters:
        - name: id
          in: path
          required: true
          description: 财务记录ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApproveFinancialRecordRequest"
      responses:
        "200":
          description: 审核成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"

  /financial/stats:
    get:
      summary: 获取财务统计数据
      description: 获取财务统计汇总数据
      tags:
        - 财务统计
      parameters:
        - name: startDate
          in: query
          description: 统计开始日期
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: 统计结束日期
          schema:
            type: string
            format: date
      responses:
        "200":
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/FinancialStats"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

  /financial/export:
    get:
      summary: 导出财务数据
      description: 导出财务数据为Excel文件
      tags:
        - 财务导出
      parameters:
        - name: type
          in: query
          description: 类型筛选
          schema:
            $ref: "#/components/schemas/FinancialType"
        - name: category
          in: query
          description: 分类筛选
          schema:
            $ref: "#/components/schemas/FinancialCategory"
        - name: startDate
          in: query
          description: 开始日期
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: 结束日期
          schema:
            type: string
            format: date
      responses:
        "200":
          description: 导出成功
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    FinancialType:
      type: string
      enum:
        - INCOME
        - EXPENSE
      description: 财务类型

    FinancialCategory:
      type: string
      enum:
        - PROJECT_INCOME
        - OFFICE_SUPPLIES
        - TRAVEL
        - TRAINING
        - OTHER
      description: 财务分类

    FinancialStatus:
      type: string
      enum:
        - PENDING
        - APPROVED
        - REJECTED
      description: 财务记录状态

    FinancialRecord:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 记录ID
        type:
          $ref: "#/components/schemas/FinancialType"
        category:
          $ref: "#/components/schemas/FinancialCategory"
        amount:
          type: integer
          format: int64
          description: 金额（单位：分）
          minimum: 1
        description:
          type: string
          description: 描述
          maxLength: 500
        date:
          type: string
          format: date
          description: 日期
        status:
          $ref: "#/components/schemas/FinancialStatus"
        createdBy:
          type: string
          description: 创建人
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
      required:
        - id
        - type
        - category
        - amount
        - description
        - date
        - status
        - createdBy
        - createdAt
        - updatedAt

    CreateFinancialRecordRequest:
      type: object
      properties:
        type:
          $ref: "#/components/schemas/FinancialType"
        category:
          $ref: "#/components/schemas/FinancialCategory"
        amount:
          type: integer
          format: int64
          description: 金额（单位：分）
          minimum: 1
        description:
          type: string
          description: 描述
          minLength: 1
          maxLength: 500
        date:
          type: string
          format: date
          description: 日期
      required:
        - type
        - category
        - amount
        - description
        - date

    UpdateFinancialRecordRequest:
      type: object
      properties:
        type:
          $ref: "#/components/schemas/FinancialType"
        category:
          $ref: "#/components/schemas/FinancialCategory"
        amount:
          type: integer
          format: int64
          description: 金额（单位：分）
          minimum: 1
        description:
          type: string
          description: 描述
          minLength: 1
          maxLength: 500
        date:
          type: string
          format: date
          description: 日期
      required:
        - type
        - category
        - amount
        - description
        - date

    ApproveFinancialRecordRequest:
      type: object
      properties:
        action:
          type: string
          enum:
            - APPROVE
            - REJECT
          description: 审核动作
        comment:
          type: string
          description: 审核意见
          maxLength: 500
      required:
        - action

    FinancialStats:
      type: object
      properties:
        totalIncome:
          type: integer
          format: int64
          description: 总收入（单位：分）
        totalExpense:
          type: integer
          format: int64
          description: 总支出（单位：分）
        totalProfit:
          type: integer
          format: int64
          description: 净利润（单位：分）
        pendingCount:
          type: integer
          description: 待审核数量
      required:
        - totalIncome
        - totalExpense
        - totalProfit
        - pendingCount

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
          minimum: 1
        size:
          type: integer
          description: 每页数量
          minimum: 1
        total:
          type: integer
          description: 总记录数
          minimum: 0
        totalPages:
          type: integer
          description: 总页数
          minimum: 0
      required:
        - page
        - size
        - total
        - totalPages

    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 请求是否成功
        message:
          type: string
          description: 响应消息
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
      required:
        - success
        - message
        - timestamp

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 请求是否成功
          example: false
        message:
          type: string
          description: 错误消息
        code:
          type: string
          description: 错误代码
        errors:
          type: array
          description: 详细错误信息
          items:
            type: object
            properties:
              field:
                type: string
                description: 错误字段
              message:
                type: string
                description: 错误描述
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
      required:
        - success
        - message
        - timestamp

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "请求参数验证失败"
            code: "INVALID_PARAMS"
            errors:
              - field: "amount"
                message: "金额必须大于0"
            timestamp: "2024-01-15T10:30:00Z"

    Unauthorized:
      description: 未认证
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "认证失败，请重新登录"
            code: "UNAUTHORIZED"
            timestamp: "2024-01-15T10:30:00Z"

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "权限不足"
            code: "INSUFFICIENT_PERMISSION"
            timestamp: "2024-01-15T10:30:00Z"

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            success: false
            message: "财务记录不存在"
            code: "RECORD_NOT_FOUND"
            timestamp: "2024-01-15T10:30:00Z"

tags:
  - name: 财务记录
    description: 财务记录相关接口
  - name: 财务统计
    description: 财务统计相关接口
  - name: 财务导出
    description: 财务数据导出接口
