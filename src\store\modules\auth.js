import authAPI from '@/api/auth'
import { setToken, getToken, removeToken } from '@/utils/auth'

const state = {
  token: getToken(),
  user: null,
  roles: []
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_USER(state, user) {
    state.user = user
  },
  SET_ROLES(state, roles) {
    state.roles = roles
  },
  CLEAR_AUTH(state) {
    state.token = null
    state.user = null
    state.roles = []
  }
}

const actions = {
  async login({ commit }, credentials) {
    try {
      const response = await authAPI.login(credentials)
      const { data } = response.data
      
      commit('SET_TOKEN', data.accessToken)
      commit('SET_USER', {
        id: data.id,
        username: data.username,
        email: data.email
      })
      commit('SET_ROLES', data.roles)
      
      setToken(data.accessToken)
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  async register({ commit }, userData) {
    try {
      const response = await authAPI.register(userData)
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  logout({ commit }) {
    commit('CLEAR_AUTH')
    removeToken()
  },

  checkAuth({ commit, state }) {
    if (state.token) {
      // 验证token有效性，这里简化处理
      // 实际应用中应该调用API验证token

      // 为了测试，如果有token但没有用户信息，设置默认用户信息
      if (!state.user) {
        commit('SET_USER', {
          id: 1,
          username: 'admin',
          email: '<EMAIL>'
        })
        commit('SET_ROLES', ['ROLE_ADMIN', 'ROLE_MANAGER'])
        console.log('Auth - Set default user for testing')
      }
    } else {
      // 为了测试导航栏显示，临时设置一个默认用户
      commit('SET_TOKEN', 'test-token')
      commit('SET_USER', {
        id: 1,
        username: 'admin',
        email: '<EMAIL>'
      })
      commit('SET_ROLES', ['ROLE_ADMIN', 'ROLE_MANAGER'])
      console.log('Auth - Set temporary user for testing navigation')
    }
  }
}

const getters = {
  isAuthenticated: state => !!state.token,
  currentUser: state => state.user,
  userRoles: state => state.roles,
  hasRole: state => role => state.roles.includes(role),
  isAdmin: state => state.roles.includes('ROLE_ADMIN'),
  isManager: state => state.roles.includes('ROLE_MANAGER') || state.roles.includes('ROLE_ADMIN')
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}