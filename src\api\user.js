import request from './request'

export default {
  getUsers(params) {
    return request({
      url: '/api/users',
      method: 'get',
      params
    })
  },
  
  getUserById(id) {
    return request({
      url: `/users/${id}`,
      method: 'get'
    })
  },
  
  updateUser(id, data) {
    return request({
      url: `/users/${id}`,
      method: 'put',
      data
    })
  },
  
  deleteUser(id) {
    return request({
      url: `/users/${id}`,
      method: 'delete'
    })
  },
  
  toggleUserStatus(id) {
    return request({
      url: `/users/${id}/status`,
      method: 'put'
    })
  }
}