// API连接测试工具
import request from '@/api/request'

/**
 * 测试后端连接
 */
export async function testBackendConnection() {
  try {
    console.log('🔍 开始测试后端连接...')
    
    // 测试基础连接
    const response = await request({
      url: '/health',
      method: 'get',
      timeout: 5000
    })
    
    console.log('✅ 后端连接成功:', response.data)
    return { success: true, data: response.data }
  } catch (error) {
    console.error('❌ 后端连接失败:', error)
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🚫 连接被拒绝 - 请检查后端服务是否在 localhost:8080 运行')
    } else if (error.code === 'NETWORK_ERROR') {
      console.error('🌐 网络错误 - 请检查网络连接')
    } else if (error.response?.status === 404) {
      console.error('🔍 接口不存在 - 后端可能没有 /api/health 接口')
    }
    
    return { success: false, error: error.message }
  }
}

/**
 * 测试认证API
 */
export async function testAuthAPI() {
  try {
    console.log('🔐 测试认证API...')
    
    // 测试登录接口（使用无效凭据，只测试接口是否存在）
    const response = await request({
      url: '/auth/signin',
      method: 'post',
      data: { username: 'test', password: 'test' }
    })
    
    console.log('✅ 认证API响应:', response.data)
    return { success: true }
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 400) {
      console.log('✅ 认证API存在（返回预期的错误状态）')
      return { success: true }
    }
    
    console.error('❌ 认证API测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试财务API
 */
export async function testFinancialAPI() {
  try {
    console.log('💰 测试财务API...')
    
    const response = await request({
      url: '/financial/records',
      method: 'get',
      params: { page: 1, size: 10 }
    })
    
    console.log('✅ 财务API响应:', response.data)
    return { success: true, data: response.data }
  } catch (error) {
    console.error('❌ 财务API测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行所有API测试
 */
export async function runAllTests() {
  console.log('🚀 开始API连接测试...')
  console.log('📍 前端地址: http://localhost:8081')
  console.log('📍 后端地址: http://localhost:8080')
  console.log('📍 代理配置: /api -> http://localhost:8080')
  console.log('---')
  
  const results = {
    backend: await testBackendConnection(),
    auth: await testAuthAPI(),
    financial: await testFinancialAPI()
  }
  
  console.log('---')
  console.log('📊 测试结果汇总:')
  console.log('后端连接:', results.backend.success ? '✅' : '❌')
  console.log('认证API:', results.auth.success ? '✅' : '❌')
  console.log('财务API:', results.financial.success ? '✅' : '❌')
  
  const allSuccess = Object.values(results).every(r => r.success)
  
  if (allSuccess) {
    console.log('🎉 所有测试通过！前后端连接正常')
  } else {
    console.log('⚠️  部分测试失败，请检查后端服务状态')
  }
  
  return results
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  window.testAPI = {
    testBackendConnection,
    testAuthAPI,
    testFinancialAPI,
    runAllTests
  }
  
  console.log('💡 API测试工具已加载，在控制台中使用:')
  console.log('   window.testAPI.runAllTests() - 运行所有测试')
  console.log('   window.testAPI.testBackendConnection() - 测试后端连接')
}
